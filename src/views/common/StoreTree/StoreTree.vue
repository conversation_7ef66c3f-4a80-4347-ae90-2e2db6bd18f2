<template>
  <div>

    <a-input-search  v-model="searchValue" style="margin-bottom: 8px" placeholder="店铺查询" @change="onChange" />

    <a-tree
      v-if="storeList.length>0"
      :tree-data="storeListFilt"
      @select="getSelectStore"
    >
    </a-tree>


  </div>
</template>

<script>
  import {getAction} from '@/api/manage';
  export default {
    name: 'StoreTree',
    data(){
      return{
        storeList:[],
        storeListFilt:[],
        searchValue:'',
        url:{
          getAllStoreList:"storeManage/storeManage/getAllStoreList",
        }
      }
    },
    created() {
      this.getAllStoreList();
    },
    methods: {
      getSelectStore(e){
        console.log(e);
        let storeInfo=this.storeListFilt.find(item=>item.key===e[0]);
        this.$emit('ok',storeInfo);
      },
      onChange(e){
        console.log(this.searchValue);
        if(this.searchValue) {
          // 过滤店铺列表，但始终保留"全部店铺"选项
          const allStoresOption = this.storeList.find(item => item.key === 'ALL_STORES');
          const filteredStores = this.storeList.filter(item =>
            item.key === 'ALL_STORES' || item.title.indexOf(this.searchValue) > -1
          );
          this.storeListFilt = filteredStores;
        }else{
          this.storeListFilt=this.storeList;
        }
      },
      getAllStoreList() {
        getAction(this.url.getAllStoreList, {}).then((res) => {

          if (res.success) {
            // 添加"全部店铺"选项到列表顶部
            this.storeList.push({
              title: '🏪 全部店铺',
              key: 'ALL_STORES'
            });
            this.storeListFilt.push({
              title: '🏪 全部店铺',
              key: 'ALL_STORES'
            });

            for (let s of res.result) {
              this.storeList.push({
                title: s.storeName,
                key: s.id
              });
              this.storeListFilt.push({
                title: s.storeName,
                key: s.id
              });
            }
            console.log(this.storeList);
          } else {
            this.$message.warning(res.message);
          }
        });
      }
    }
  }
</script>

<style scoped>

</style>