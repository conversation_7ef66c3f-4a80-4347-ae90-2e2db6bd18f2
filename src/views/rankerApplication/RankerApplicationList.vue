<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="申请人手机号">
              <a-input placeholder="请输入申请人手机号" v-model="queryParam.memberPhone"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="申请状态">
              <j-dict-select-tag placeholder="请选择申请状态" v-model="queryParam.applicationStatus" dictCode="ranker_application_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="支付状态">
              <j-dict-select-tag placeholder="请选择支付状态" v-model="queryParam.paymentStatus" dictCode="ranker_payment_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons" style="display: flex;white-space: nowrap; overflow: hidden;">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <!-- <a-button type="primary" icon="download" @click="handleExportXls('打榜者申请表')">导出</a-button> -->
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query> -->
      <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
           <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item> 
          <a-menu-item key="2" @click="batchAudit('1')"><a-icon type="check"/>批量通过</a-menu-item>
          <a-menu-item key="3" @click="batchAudit('2')"><a-icon type="close"/>批量拒绝</a-menu-item> 
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
           <a v-if="record.applicationStatus === '0'" @click="handleAudit(record, '1')">审核通过</a>
            <a-divider type="vertical" />
           <a v-if="record.applicationStatus === '0'" @click="handleAudit(record, '2')">审核拒绝</a>
          <!-- <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" /> -->
          <!-- <a @click="handleDetail(record)">详情</a> -->
          <!-- <a-divider type="vertical" /> -->
          <!-- <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item v-if="record.applicationStatus === '0'">
                <a @click="handleAudit(record, '1')">审核通过</a>
              </a-menu-item>
              <a-menu-item v-if="record.applicationStatus === '0'">
                <a @click="handleAudit(record, '2')">审核拒绝</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown> -->
        </span>

      </a-table>
    </div>

    <ranker-application-modal ref="modalForm" @ok="modalFormOk"></ranker-application-modal>
    <ranker-application-audit-modal ref="auditModal" @ok="modalFormOk"></ranker-application-audit-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import RankerApplicationModal from './modules/RankerApplicationModal'
  import RankerApplicationAuditModal from './modules/RankerApplicationAuditModal'
  import pick from 'lodash.pick'

  export default {
    name: 'RankerApplicationList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      RankerApplicationModal,
      RankerApplicationAuditModal
    },
    data () {
      return {
        description: '打榜者申请管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'申请人手机号',
            align:"center",
            dataIndex: 'memberPhone'
          },
          {
            title:'申请人姓名',
            align:"center",
            dataIndex: 'memberName'
          },
          {
            title:'推荐人手机号',
            align:"center",
            dataIndex: 'recommenderPhone'
          },
          {
            title:'推荐人姓名',
            align:"center",
            dataIndex: 'recommenderName'
          },
          {
            title:'申请类型',
            align:"center",
            dataIndex: 'applicationType',
            customRender: function (text, record) {
              if (record.paymentAmount && parseFloat(record.paymentAmount) > 0) {
                return '付费申请';
              } else {
                return '审核申请';
              }
            }
          },
          {
            title:'支付金额',
            align:"center",
            dataIndex: 'paymentAmount'
          },
          {
            title:'支付状态',
            align:"center",
            dataIndex: 'paymentStatus_dictText'
          },
          {
            title:'申请状态',
            align:"center",
            dataIndex: 'applicationStatus_dictText'
          },
          {
            title:'申请时间',
            align:"center",
            dataIndex: 'createTime'
          },
          {
            title:'审核时间',
            align:"center",
            dataIndex: 'auditTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/rankerApplication/rankerApplication/listWithApplicantInfo",
          delete: "/rankerApplication/rankerApplication/delete",
          deleteBatch: "/rankerApplication/rankerApplication/deleteBatch",
          exportXlsUrl: "/rankerApplication/rankerApplication/exportXls",
          importExcelUrl: "rankerApplication/rankerApplication/importExcel",
          audit: "/rankerApplication/rankerApplication/audit",
          batchAudit: "/rankerApplication/rankerApplication/batchAudit"
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'memberPhone',text:'申请人手机号'})
        fieldList.push({type:'string',value:'memberName',text:'申请人姓名'})
        fieldList.push({type:'string',value:'recommenderPhone',text:'推荐人手机号'})
        fieldList.push({type:'string',value:'recommenderName',text:'推荐人姓名'})
        fieldList.push({type:'int',value:'paymentAmount',text:'支付金额'})
        fieldList.push({type:'string',value:'paymentStatus',text:'支付状态',dictCode:'ranker_payment_status'})
        fieldList.push({type:'string',value:'applicationStatus',text:'申请状态',dictCode:'ranker_application_status'})
        fieldList.push({type:'datetime',value:'createTime',text:'申请时间'})
        fieldList.push({type:'datetime',value:'auditTime',text:'审核时间'})
        this.superFieldList = fieldList
      },
      handleAudit(record, status) {
        this.$refs.auditModal.show(record, status);
        this.$refs.auditModal.title = status === '1' ? "审核通过" : "审核拒绝";
      },
      batchAudit(status) {
        if(this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！');
          return false;
        } else {
          this.$refs.auditModal.showBatch(this.selectedRowKeys, status);
          this.$refs.auditModal.title = status === '1' ? "批量审核通过" : "批量审核拒绝";
        }
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
