<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="申请人" prop="memberId">
              <j-select-user-by-dep placeholder="请选择申请人" v-model="model.memberId" :disabled="!!model.id"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="推荐人手机号" prop="recommenderPhone">
              <a-input v-model="model.recommenderPhone" placeholder="请输入推荐人手机号"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="推荐人姓名" prop="recommenderName">
              <a-input v-model="model.recommenderName" placeholder="请输入推荐人姓名"></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="支付金额" prop="paymentAmount">
              <a-input-number v-model="model.paymentAmount" placeholder="请输入支付金额" :precision="2" style="width: 100%"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="支付状态" prop="paymentStatus">
              <j-dict-select-tag type="list" v-model="model.paymentStatus" dictCode="ranker_payment_status" placeholder="请选择支付状态"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="申请状态" prop="applicationStatus">
              <j-dict-select-tag type="list" v-model="model.applicationStatus" dictCode="ranker_application_status" placeholder="请选择申请状态"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="审核时间" prop="auditTime">
              <j-date placeholder="请选择审核时间" v-model="model.auditTime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="审核人员" prop="auditUser">
              <j-select-user-by-dep placeholder="请选择审核人员" v-model="model.auditUser"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="生效时间" prop="effectiveTime">
              <j-date placeholder="请选择生效时间" v-model="model.effectiveTime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="申请理由" prop="applicationReason">
              <a-textarea v-model="model.applicationReason" rows="4" placeholder="请输入申请理由"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="审核备注" prop="auditRemark">
              <a-textarea v-model="model.auditRemark" rows="4" placeholder="请输入审核备注"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import pick from 'lodash.pick'

  export default {
    name: 'RankerApplicationModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          memberId: [
            { required: true, message: '请选择申请人!'},
          ],
          paymentAmount: [
            { required: true, message: '请输入支付金额!'},
          ],
          paymentStatus: [
            { required: true, message: '请选择支付状态!'},
          ],
          applicationStatus: [
            { required: true, message: '请选择申请状态!'},
          ],
        },
        url: {
          add: "/rankerApplication/rankerApplication/add",
          edit: "/rankerApplication/rankerApplication/edit",
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'memberId','recommenderPhone','recommenderName','paymentAmount','paymentStatus','applicationStatus','auditTime','auditUser','auditRemark','applicationReason','effectiveTime'))
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if(valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
    }
  }
</script>
